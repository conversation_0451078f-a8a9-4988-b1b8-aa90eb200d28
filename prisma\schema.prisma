// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Tenants table - Multi-tenancy support
model Tenant {
  id        String   @id @default(cuid())
  name      String
  domain    String   @unique
  settings  Json?    @default("{}")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  users           User[]
  roles           Role[]

  @@map("tenants")
}

// Users table
model User {
  id           String    @id @default(cuid())
  email        String    @unique
  passwordHash String    @map("password_hash")
  firstName    String    @map("first_name")
  lastName     String    @map("last_name")
  tenantId     String    @map("tenant_id")
  status       UserStatus @default(ACTIVE)
  createdAt    DateTime  @default(now()) @map("created_at")
  updatedAt    DateTime  @updatedAt @map("updated_at")

  // Relations
  tenant    Tenant     @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  userRoles UserRole[]

  @@map("users")
}

// User status enum
enum UserStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
  PENDING
}

// Roles table
model Role {
  id             String   @id @default(cuid())
  name           String
  description    String?
  tenantId       String   @map("tenant_id")
  isSystemRole   Boolean  @default(false) @map("is_system_role")
  createdAt      DateTime @default(now()) @map("created_at")
  updatedAt      DateTime @updatedAt @map("updated_at")

  // Relations
  tenant          Tenant           @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  rolePermissions RolePermission[]
  userRoles       UserRole[]

  @@unique([name, tenantId])
  @@map("roles")
}

// Permissions table
model Permission {
  id          String   @id @default(cuid())
  name        String   @unique
  resource    String
  action      String
  description String?
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relations
  rolePermissions RolePermission[]

  @@unique([resource, action])
  @@map("permissions")
}

// Role-Permission mapping table
model RolePermission {
  roleId       String @map("role_id")
  permissionId String @map("permission_id")

  // Relations
  role       Role       @relation(fields: [roleId], references: [id], onDelete: Cascade)
  permission Permission @relation(fields: [permissionId], references: [id], onDelete: Cascade)

  @@id([roleId, permissionId])
  @@map("role_permissions")
}

// User-Role mapping table
model UserRole {
  userId     String   @map("user_id")
  roleId     String   @map("role_id")
  assignedBy String?  @map("assigned_by")
  assignedAt DateTime @default(now()) @map("assigned_at")

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  role Role @relation(fields: [roleId], references: [id], onDelete: Cascade)

  @@id([userId, roleId])
  @@map("user_roles")
}