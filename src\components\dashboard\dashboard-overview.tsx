"use client"

import { <PERSON>, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card"

export function DashboardOverview() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Overview</CardTitle>
        <CardDescription>
          Your business performance at a glance
        </CardDescription>
      </CardHeader>
      <CardContent className="pl-2">
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <p className="text-sm font-medium">Sales This Month</p>
              <p className="text-2xl font-bold">$45,231.89</p>
              <p className="text-xs text-slate-500 dark:text-slate-400">
                +20.1% from last month
              </p>
            </div>
            <div className="space-y-2">
              <p className="text-sm font-medium">Orders This Month</p>
              <p className="text-2xl font-bold">234</p>
              <p className="text-xs text-slate-500 dark:text-slate-400">
                +15.3% from last month
              </p>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <p className="text-sm font-medium">Active Products</p>
              <p className="text-2xl font-bold">567</p>
              <p className="text-xs text-slate-500 dark:text-slate-400">
                +8.2% from last month
              </p>
            </div>
            <div className="space-y-2">
              <p className="text-sm font-medium">Low Stock Items</p>
              <p className="text-2xl font-bold text-orange-600">12</p>
              <p className="text-xs text-slate-500 dark:text-slate-400">
                Requires attention
              </p>
            </div>
          </div>

          <div className="pt-4 border-t border-slate-200 dark:border-slate-800">
            <h4 className="text-sm font-medium mb-3">Recent Highlights</h4>
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span className="text-slate-500 dark:text-slate-400">Best Selling Product</span>
                <span className="font-medium">Business Laptop</span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-slate-500 dark:text-slate-400">Top Customer</span>
                <span className="font-medium">ABC Corporation</span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-slate-500 dark:text-slate-400">Avg. Order Value</span>
                <span className="font-medium">$193.45</span>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
