import { create } from 'zustand'
import { devtools } from 'zustand/middleware'

interface Product {
  id: string
  name: string
  description?: string
  sku: string
  barcode?: string
  categoryId: string
  unitPrice: number
  costPrice: number
  status: 'ACTIVE' | 'INACTIVE' | 'DISCONTINUED'
  createdAt: Date
  updatedAt: Date
  category?: {
    id: string
    name: string
  }
}

interface ProductState {
  products: Product[]
  selectedProduct: Product | null
  isLoading: boolean
  searchQuery: string
  filters: {
    category?: string
    status?: string
  }
  setProducts: (products: Product[]) => void
  setSelectedProduct: (product: Product | null) => void
  setLoading: (loading: boolean) => void
  setSearchQuery: (query: string) => void
  setFilters: (filters: Partial<ProductState['filters']>) => void
  addProduct: (product: Product) => void
  updateProduct: (id: string, product: Partial<Product>) => void
  removeProduct: (id: string) => void
}

export const useProductStore = create<ProductState>()(
  devtools(
    (set, get) => ({
      products: [],
      selectedProduct: null,
      isLoading: false,
      searchQuery: '',
      filters: {},
      setProducts: (products) => set({ products }),
      setSelectedProduct: (selectedProduct) => set({ selectedProduct }),
      setLoading: (isLoading) => set({ isLoading }),
      setSearchQuery: (searchQuery) => set({ searchQuery }),
      setFilters: (filters) => set((state) => ({ 
        filters: { ...state.filters, ...filters } 
      })),
      addProduct: (product) => set((state) => ({ 
        products: [...state.products, product] 
      })),
      updateProduct: (id, updatedProduct) => set((state) => ({
        products: state.products.map((product) =>
          product.id === id ? { ...product, ...updatedProduct } : product
        ),
      })),
      removeProduct: (id) => set((state) => ({
        products: state.products.filter((product) => product.id !== id),
      })),
    }),
    {
      name: 'product-store',
    }
  )
)
