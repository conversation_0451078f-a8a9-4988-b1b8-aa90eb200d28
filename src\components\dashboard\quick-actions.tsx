"use client"

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { 
  Plus, 
  Package, 
  Users, 
  ShoppingCart, 
  FileText,
  BarChart3
} from "lucide-react"
import Link from "next/link"

const quickActions = [
  {
    title: "Add Product",
    description: "Create a new product in inventory",
    icon: Package,
    href: "/products/new",
    color: "bg-blue-500 hover:bg-blue-600",
  },
  {
    title: "Create Order",
    description: "Process a new customer order",
    icon: ShoppingCart,
    href: "/orders/new",
    color: "bg-green-500 hover:bg-green-600",
  },
  {
    title: "Add User",
    description: "Register a new system user",
    icon: Users,
    href: "/users/new",
    color: "bg-purple-500 hover:bg-purple-600",
  },
  {
    title: "Generate Report",
    description: "Create business reports",
    icon: BarChart3,
    href: "/reports",
    color: "bg-orange-500 hover:bg-orange-600",
  },
  {
    title: "View Inventory",
    description: "Check current stock levels",
    icon: FileText,
    href: "/inventory",
    color: "bg-teal-500 hover:bg-teal-600",
  },
  {
    title: "Add Category",
    description: "Create product categories",
    icon: Plus,
    href: "/categories/new",
    color: "bg-pink-500 hover:bg-pink-600",
  },
]

export function QuickActions() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Quick Actions</CardTitle>
        <CardDescription>
          Frequently used actions for efficient workflow
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
          {quickActions.map((action) => {
            const Icon = action.icon
            return (
              <Link key={action.title} href={action.href}>
                <Button
                  variant="outline"
                  className="h-auto p-4 flex flex-col items-center space-y-2 hover:shadow-md transition-shadow"
                >
                  <div className={`p-2 rounded-lg text-white ${action.color}`}>
                    <Icon className="h-5 w-5" />
                  </div>
                  <div className="text-center">
                    <p className="font-medium text-sm">{action.title}</p>
                    <p className="text-xs text-slate-500 dark:text-slate-400">
                      {action.description}
                    </p>
                  </div>
                </Button>
              </Link>
            )
          })}
        </div>
      </CardContent>
    </Card>
  )
}
