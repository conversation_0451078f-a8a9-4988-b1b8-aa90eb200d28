import type { Metadata } from "next";
import { DashboardOverview } from "@/components/dashboard/dashboard-overview";
import { RecentActivity } from "@/components/dashboard/recent-activity";
import { QuickActions } from "@/components/dashboard/quick-actions";
import { StatsCards } from "@/components/dashboard/stats-cards";

export const metadata: Metadata = {
  title: "Dashboard | CLOVER ERP",
  description: "CLOVER ERP Dashboard - Manage your business operations",
};

export default function Dashboard() {
  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <div className="flex items-center justify-between space-y-2">
        <h2 className="text-3xl font-bold tracking-tight">Dashboard</h2>
      </div>

      <div className="space-y-4">
        <StatsCards />

        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
          <div className="col-span-4">
            <DashboardOverview />
          </div>
          <div className="col-span-3">
            <RecentActivity />
          </div>
        </div>

        <QuickActions />
      </div>
    </div>
  );
}
