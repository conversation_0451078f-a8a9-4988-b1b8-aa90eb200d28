import { DefaultSession, DefaultUser } from "next-auth"
import { JWT, DefaultJWT } from "next-auth/jwt"

declare module "next-auth" {
  interface Session {
    user: {
      id: string
      role: string
      department?: string
    } & DefaultSession["user"]
  }

  interface User extends Default<PERSON>ser {
    role: string
    department?: string
  }
}

declare module "next-auth/jwt" {
  interface JWT extends DefaultJWT {
    role: string
    department?: string
  }
}
