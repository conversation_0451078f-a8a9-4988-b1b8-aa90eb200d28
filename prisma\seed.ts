import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Starting database seeding...')

  // Create default permissions
  console.log('📝 Creating default permissions...')
  const permissions = [
    // User management
    { name: 'users.create', resource: 'users', action: 'create', description: 'Create new users' },
    { name: 'users.read', resource: 'users', action: 'read', description: 'View users' },
    { name: 'users.update', resource: 'users', action: 'update', description: 'Update user information' },
    { name: 'users.delete', resource: 'users', action: 'delete', description: 'Delete users' },

    // Role management
    { name: 'roles.create', resource: 'roles', action: 'create', description: 'Create new roles' },
    { name: 'roles.read', resource: 'roles', action: 'read', description: 'View roles' },
    { name: 'roles.update', resource: 'roles', action: 'update', description: 'Update role information' },
    { name: 'roles.delete', resource: 'roles', action: 'delete', description: 'Delete roles' },

    // Permission management
    { name: 'permissions.read', resource: 'permissions', action: 'read', description: 'View permissions' },
    { name: 'permissions.assign', resource: 'permissions', action: 'assign', description: 'Assign permissions to roles' },

    // Tenant management
    { name: 'tenants.create', resource: 'tenants', action: 'create', description: 'Create new tenants' },
    { name: 'tenants.read', resource: 'tenants', action: 'read', description: 'View tenant information' },
    { name: 'tenants.update', resource: 'tenants', action: 'update', description: 'Update tenant settings' },
    { name: 'tenants.delete', resource: 'tenants', action: 'delete', description: 'Delete tenants' },

    // Dashboard
    { name: 'dashboard.read', resource: 'dashboard', action: 'read', description: 'View dashboard' },

    // Reports
    { name: 'reports.read', resource: 'reports', action: 'read', description: 'View reports' },
    { name: 'reports.create', resource: 'reports', action: 'create', description: 'Create reports' },
    { name: 'reports.export', resource: 'reports', action: 'export', description: 'Export reports' },

    // Settings
    { name: 'settings.read', resource: 'settings', action: 'read', description: 'View settings' },
    { name: 'settings.update', resource: 'settings', action: 'update', description: 'Update settings' },
  ]

  for (const permission of permissions) {
    await prisma.permission.upsert({
      where: { name: permission.name },
      update: {},
      create: permission,
    })
  }

  // Create default tenant
  console.log('🏢 Creating default tenant...')
  const defaultTenant = await prisma.tenant.upsert({
    where: { domain: 'localhost' },
    update: {},
    create: {
      name: 'CLOVER ERP',
      domain: 'localhost',
      settings: {
        theme: 'light',
        timezone: 'UTC',
        dateFormat: 'YYYY-MM-DD',
        currency: 'USD',
      },
    },
  })

  // Create system roles
  console.log('👥 Creating system roles...')
  const superAdminRole = await prisma.role.upsert({
    where: {
      name_tenantId: {
        name: 'Super Admin',
        tenantId: defaultTenant.id
      }
    },
    update: {},
    create: {
      name: 'Super Admin',
      description: 'Full system access with all permissions',
      tenantId: defaultTenant.id,
      isSystemRole: true,
    },
  })

  const adminRole = await prisma.role.upsert({
    where: {
      name_tenantId: {
        name: 'Admin',
        tenantId: defaultTenant.id
      }
    },
    update: {},
    create: {
      name: 'Admin',
      description: 'Administrative access with most permissions',
      tenantId: defaultTenant.id,
      isSystemRole: true,
    },
  })

  const userRole = await prisma.role.upsert({
    where: {
      name_tenantId: {
        name: 'User',
        tenantId: defaultTenant.id
      }
    },
    update: {},
    create: {
      name: 'User',
      description: 'Basic user access with limited permissions',
      tenantId: defaultTenant.id,
      isSystemRole: true,
    },
  })

  // Assign all permissions to Super Admin
  console.log('🔐 Assigning permissions to roles...')
  const allPermissions = await prisma.permission.findMany()

  for (const permission of allPermissions) {
    await prisma.rolePermission.upsert({
      where: {
        roleId_permissionId: {
          roleId: superAdminRole.id,
          permissionId: permission.id,
        },
      },
      update: {},
      create: {
        roleId: superAdminRole.id,
        permissionId: permission.id,
      },
    })
  }

  // Assign most permissions to Admin (exclude tenant management)
  const adminPermissions = allPermissions.filter(p => !p.name.startsWith('tenants.'))
  for (const permission of adminPermissions) {
    await prisma.rolePermission.upsert({
      where: {
        roleId_permissionId: {
          roleId: adminRole.id,
          permissionId: permission.id,
        },
      },
      update: {},
      create: {
        roleId: adminRole.id,
        permissionId: permission.id,
      },
    })
  }

  // Assign basic permissions to User
  const userPermissions = allPermissions.filter(p =>
    p.name === 'dashboard.read' ||
    p.name === 'reports.read' ||
    p.name === 'settings.read'
  )
  for (const permission of userPermissions) {
    await prisma.rolePermission.upsert({
      where: {
        roleId_permissionId: {
          roleId: userRole.id,
          permissionId: permission.id,
        },
      },
      update: {},
      create: {
        roleId: userRole.id,
        permissionId: permission.id,
      },
    })
  }

  // Create default super admin user
  console.log('👤 Creating default super admin user...')
  const hashedPassword = await bcrypt.hash('admin123', 12)

  const superAdminUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      passwordHash: hashedPassword,
      firstName: 'Super',
      lastName: 'Admin',
      tenantId: defaultTenant.id,
      status: 'ACTIVE',
    },
  })

  // Assign Super Admin role to the user
  await prisma.userRole.upsert({
    where: {
      userId_roleId: {
        userId: superAdminUser.id,
        roleId: superAdminRole.id,
      },
    },
    update: {},
    create: {
      userId: superAdminUser.id,
      roleId: superAdminRole.id,
      assignedBy: superAdminUser.id,
    },
  })

  console.log('✅ Database seeding completed successfully!')
  console.log(`📧 Super Admin Email: <EMAIL>`)
  console.log(`🔑 Super Admin Password: admin123`)
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
